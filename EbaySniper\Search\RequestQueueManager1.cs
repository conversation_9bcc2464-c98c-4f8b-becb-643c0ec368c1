using System;
using System.Threading;
using System.Threading.Tasks;

namespace uBuyFirst.Search
{
    /// <summary>
    /// Centralized manager for request queues and thread limiters
    /// Implements singleton pattern for global access
    /// </summary>
    public class RequestQueueManager
    {
        private static readonly Lazy<RequestQueueManager> s_instance = new Lazy<RequestQueueManager>(() => new RequestQueueManager());

        // Flag to track whether we've been properly initialized
        private bool _hasBeenInitialized = false;

        /// <summary>
        /// Gets the singleton instance of the request queue manager
        /// </summary>
        public static RequestQueueManager Instance => s_instance.Value;

        // Queue counters
        private int _generalItemQueueCount;

        /// <summary>
        /// Gets the current count of general item queue requests
        /// </summary>
        public int GeneralItemQueueCount => _generalItemQueueCount;

        // Thread limiters
        private SemaphoreSlim _getItemQueueLimiter;
        private SemaphoreSlim _browseAPIGetItemQueueLimiter;
        private SemaphoreSlim _llmQueueLimiter;

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private RequestQueueManager()
        {
            // Initialize with default values - will be updated later
            // Use a reasonable default value that will be updated properly
            int defaultMaxThreads = 5;
            _getItemQueueLimiter = new SemaphoreSlim(defaultMaxThreads);
            _browseAPIGetItemQueueLimiter = new SemaphoreSlim(defaultMaxThreads);
            _llmQueueLimiter = new SemaphoreSlim(10); // Fixed at 10 threads for LLM processing
        }

        /// <summary>
        /// Initialize the manager with configuration values from SearchConfigManager
        /// This should be called after both singletons are fully constructed
        /// </summary>
        internal void InitializeFromConfig(int maxThreads)
        {
            // Update semaphores to match the config
            UpdateLimiters(maxThreads);
        }

        /// <summary>
        /// Increments the general item queue count
        /// </summary>
        public void IncrementGeneralItemQueueCount() => Interlocked.Increment(ref _generalItemQueueCount);

        /// <summary>
        /// Decrements the general item queue count
        /// </summary>
        public void DecrementGeneralItemQueueCount() => Interlocked.Decrement(ref _generalItemQueueCount);

        public void ResetGeneralItemQueueCount() => _generalItemQueueCount = 0;

        /// <summary>
        /// Gets the GetItem queue limiter semaphore
        /// </summary>
        public SemaphoreSlim GetItemQueueLimiter => _getItemQueueLimiter;

        /// <summary>
        /// Gets the BrowseAPI GetItem queue limiter semaphore
        /// </summary>
        public SemaphoreSlim BrowseAPIGetItemQueueLimiter => _browseAPIGetItemQueueLimiter;

        /// <summary>
        /// Gets the LLM queue limiter semaphore
        /// </summary>
        public SemaphoreSlim LlmQueueLimiter => _llmQueueLimiter;

        /// <summary>
        /// Updates the capacity of the GetItem queue limiter
        /// </summary>
        /// <param name="capacity">The new capacity for the semaphore</param>
        public void UpdateLimiters(int capacity)
        {
            _getItemQueueLimiter?.Dispose();
            _getItemQueueLimiter = new SemaphoreSlim(capacity);

            // Also update the BrowseAPI limiter to match
            _browseAPIGetItemQueueLimiter?.Dispose();
            _browseAPIGetItemQueueLimiter = new SemaphoreSlim(capacity);

            // LLM limiter stays fixed at 10 threads
        }

        /// <summary>
        /// Resets all queue limiters to their maximum capacities
        /// </summary>
        /// <param name="capacity">The capacity to release</param>
        public void ResetQueueLimiters(int capacity)
        {
            try
            {
                _getItemQueueLimiter?.Release(capacity);
                _browseAPIGetItemQueueLimiter?.Release(capacity);
                _llmQueueLimiter?.Release(10); // Reset LLM to its fixed capacity
            }
            catch (SemaphoreFullException)
            {
                // Ignore if already released
            }
        }

        /// <summary>
        /// Waits for a slot in the GetItem queue
        /// Automatically tracks queue count
        /// </summary>
        public async Task WaitForGetItemQueueAsync()
        {
            IncrementGeneralItemQueueCount();
            await _getItemQueueLimiter.WaitAsync().ContinueWith(_ => DecrementGeneralItemQueueCount());
        }

        /// <summary>
        /// Waits for a slot in the BrowseAPI GetItem queue
        /// Automatically tracks queue count
        /// </summary>
        public async Task WaitForBrowseAPIQueueAsync()
        {
            IncrementGeneralItemQueueCount();
            await _browseAPIGetItemQueueLimiter.WaitAsync().ContinueWith(_ => DecrementGeneralItemQueueCount());
        }

        /// <summary>
        /// Waits for a slot in the LLM queue
        /// </summary>
        public async Task WaitForLlmQueueAsync()
        {
            await _llmQueueLimiter.WaitAsync();
        }

        /// <summary>
        /// Releases a slot in the GetItem queue
        /// </summary>
        public void ReleaseGetItemQueue()
        {
            _getItemQueueLimiter.Release();
        }

        /// <summary>
        /// Releases a slot in the BrowseAPI GetItem queue
        /// </summary>
        public void ReleaseBrowseAPIQueue()
        {
            _browseAPIGetItemQueueLimiter.Release();
        }

        /// <summary>
        /// Releases a slot in the LLM queue
        /// </summary>
        public void ReleaseLlmQueue()
        {
            _llmQueueLimiter.Release();
        }

        /// <summary>
        /// Forces release of all slots in the queue limiter
        /// </summary>
        /// <param name="count">The number of slots to release</param>
        public void ForceReleaseAll(int count)
        {
            try
            {
                _getItemQueueLimiter?.Release(count);
                _browseAPIGetItemQueueLimiter?.Release(count);
                _llmQueueLimiter?.Release(10); // Force release LLM to its fixed capacity
            }
            catch (SemaphoreFullException)
            {
                // Ignore if already released
            }
        }
    }
}
